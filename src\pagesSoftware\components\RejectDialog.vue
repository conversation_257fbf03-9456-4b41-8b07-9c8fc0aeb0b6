<template>
  <uni-popup ref="rejectDialog" type="center" :mask-click="false">
    <view class="reject-dialog">
      <view class="dialog-header">
        <text class="dialog-title">驳回任务</text>
      </view>
      <view class="dialog-content">
        <view class="reject-form">
          <view class="form-item">
            <text class="form-label">打样单编号</text>
            <text class="form-value">{{ currentItem?.sampleOrderCode || '-' }}</text>
          </view>
          <view class="form-item">
            <text class="form-label">驳回原因 <text class="required">*</text></text>
            <u-textarea
              v-model="rejectForm.rejectReason"
              placeholder="请输入驳回原因"
              :maxlength="200"
              count
            />
          </view>
        </view>
      </view>
      <view class="dialog-footer">
        <u-button
          @click="handleCancel"
          size="default"
          customStyle="flex: 1; margin-right: 10rpx;"
        >
          取消
        </u-button>
        <u-button
          type="primary"
          @click="handleConfirm"
          size="default"
          customStyle="flex: 1;"
        >
          确定
        </u-button>
      </view>
    </view>
  </uni-popup>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import type { SampleOrder } from '@/types/software/sampleOrder'

// 定义 emits
const emit = defineEmits<{
  confirm: [data: any]
  close: []
}>()

// 响应式数据
const rejectDialog = ref()
const currentItem = ref<SampleOrder | null>(null)
const rejectForm = reactive({
  rejectReason: ''
})

// 打开弹窗
const open = (item: SampleOrder) => {
  currentItem.value = item
  rejectForm.rejectReason = ''
  rejectDialog.value.open()
}

// 关闭弹窗
const close = () => {
  rejectDialog.value.close()
  currentItem.value = null
  rejectForm.rejectReason = ''
}

// 取消处理
const handleCancel = () => {
  close()
  emit('close')
}

// 确认处理
const handleConfirm = async () => {
  // 验证必填字段
  if (!rejectForm.rejectReason.trim()) {
    uni.showToast({
      title: '请输入驳回原因',
      icon: 'none',
      duration: 2000
    })
    return
  }

  if (!currentItem.value) {
    uni.showToast({
      title: '任务信息丢失，请重试',
      icon: 'error',
      duration: 2000
    })
    return
  }

  try {
    uni.showLoading({ title: '处理中...' })

    const res = await uni.$u.http.post('/software/engineerSampleOrder/updateStatus', {
      id: currentItem.value.id, // 选中的id
      status: "3", // 特殊处理，只更新"逾期情况"填写的字段
      rejectReason: rejectForm.rejectReason.trim()
    })

    if (res.code === 200) {
      // 只有成功时才关闭弹窗
      close()

      uni.showToast({
        title: '驳回成功',
        icon: 'success',
        duration: 2000
      })

      // 通知父组件
      emit('confirm', {
        id: currentItem.value.id,
        rejectReason: rejectForm.rejectReason.trim()
      })
    } else {
      // 请求失败时不关闭弹窗，显示错误信息
      uni.showToast({
        title: res.msg || '驳回失败',
        icon: 'error',
        duration: 2000
      })
    }
  } catch (error) {
    // 异常时不关闭弹窗，显示错误信息
    uni.showToast({
      title: '驳回失败',
      icon: 'error',
      duration: 2000
    })
  } finally {
    uni.hideLoading()
  }
}

// 暴露方法给父组件
defineExpose({
  open,
  close
})
</script>

<style lang="scss" scoped>
.reject-dialog {
  background: white;
  border-radius: 16rpx;
  width: 600rpx;
  overflow: hidden;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.15);

  .dialog-header {
    padding: 32rpx 32rpx 0;
    text-align: center;

    .dialog-title {
      font-size: 32rpx;
      font-weight: 600;
      color: #303133;
    }
  }

  .dialog-content {
    padding: 32rpx;

    .reject-form {
      .form-item {
        margin-bottom: 24rpx;

        .form-label {
          display: block;
          font-size: 28rpx;
          color: #303133;
          margin-bottom: 12rpx;
          font-weight: 500;

          .required {
            color: #f56c6c;
            margin-left: 4rpx;
          }
        }

        .form-value {
          display: block;
          font-size: 28rpx;
          color: #409EFF;
          font-weight: 600;
          padding: 12rpx 0;
          border-bottom: 2rpx solid #f0f0f0;
        }
      }
    }
  }

  .dialog-footer {
    padding: 0 32rpx 32rpx;
    display: flex;
    gap: 20rpx;
  }
}
</style>
